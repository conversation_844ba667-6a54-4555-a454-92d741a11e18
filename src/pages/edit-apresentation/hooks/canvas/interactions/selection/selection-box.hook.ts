import { IItem } from '@/pages/edit-apresentation/types/item.type';
import { selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useSetAtom } from 'jotai';
import Konva from 'konva';
import { useCallback, useEffect, useRef, useState } from 'react';

export interface SelectionBox {
	x: number;
	y: number;
	width: number;
	height: number;
}

interface Point {
	x: number;
	y: number;
}

const MIN_SELECTION_BOX_SIZE = 5;
const MIN_DRAG_DISTANCE = 5;

const isIntersecting = (box1: SelectionBox, box2: SelectionBox): boolean => {
	return box1.x < box2.x + box2.width && box1.x + box1.width > box2.x && box1.y < box2.y + box2.height && box1.y + box1.height > box2.y;
};

export const hasValidSelection = (box: SelectionBox): boolean => box.width >= MIN_SELECTION_BOX_SIZE && box.height >= MIN_SELECTION_BOX_SIZE;

const calculateBoundedSelectionBox = (startPoint: Point, currentPoint: Point, canvasWidth: number, canvasHeight: number): SelectionBox => {
	const x = Math.min(startPoint.x, currentPoint.x);
	const y = Math.min(startPoint.y, currentPoint.y);
	const width = Math.abs(currentPoint.x - startPoint.x);
	const height = Math.abs(currentPoint.y - startPoint.y);

	const boundedX = Math.max(0, Math.min(x, canvasWidth));
	const boundedY = Math.max(0, Math.min(y, canvasHeight));
	const boundedWidth = Math.min(width, canvasWidth - boundedX);
	const boundedHeight = Math.min(height, canvasHeight - boundedY);

	return { x: boundedX, y: boundedY, width: boundedWidth, height: boundedHeight };
};

const getSelectedItemIds = (items: IItem[], selectionBox: SelectionBox): string[] => {
	return items
		.filter((item) =>
			isIntersecting(selectionBox, {
				x: item.position.x,
				y: item.position.y,
				width: item.size.width,
				height: item.size.height,
			}),
		)
		.map((item) => item.tempId)
		.filter((id): id is string => id !== null && id !== undefined);
};

export const useSelectionBox = (items: IItem[], isDraggingItem: boolean, canvasWidth: number, canvasHeight: number, padding: number = 0) => {
	const [selectionBox, setSelectionBox] = useState<SelectionBox | null>(null);
	const setSelectedIds = useSetAtom(selectedItemsIdsAtom);
	const initialPoint = useRef<Point | null>(null);
	const isDragging = useRef<boolean>(false);

	const handleMouseDown = useCallback(
		(e: Konva.KonvaEventObject<MouseEvent>) => {
			if (e.evt.button !== 0 || isDraggingItem) return;

			const pointer = e.target.getStage()?.getPointerPosition();
			if (pointer) {
				initialPoint.current = { x: pointer.x, y: pointer.y };
				isDragging.current = false;
			}
		},
		[isDraggingItem],
	);

	const handleMouseMoveSelectionBox = useCallback(
		(e: Konva.KonvaEventObject<MouseEvent>) => {
			if (isDraggingItem || !initialPoint.current) return;

			const pointer = e.target.getStage()?.getPointerPosition();
			if (!pointer) return;

			const dx = Math.abs(pointer.x - initialPoint.current.x);
			const dy = Math.abs(pointer.y - initialPoint.current.y);

			if (dx > MIN_DRAG_DISTANCE || dy > MIN_DRAG_DISTANCE) {
				isDragging.current = true;

				const adjustedInitialPoint = {
					x: initialPoint.current.x - padding,
					y: initialPoint.current.y - padding,
				};

				const adjustedPointer = {
					x: pointer.x - padding,
					y: pointer.y - padding,
				};

				setSelectionBox(calculateBoundedSelectionBox(adjustedInitialPoint, adjustedPointer, canvasWidth, canvasHeight));
			}
		},
		[isDraggingItem, canvasWidth, canvasHeight, padding],
	);

	const handleMouseUp = useCallback(() => {
		if (!initialPoint.current) return;

		if (selectionBox && hasValidSelection(selectionBox) && isDragging.current) {
			const newSelectedIds = getSelectedItemIds(items, selectionBox);
			setSelectedIds(newSelectedIds);
		}

		setSelectionBox(null);
		initialPoint.current = null;
		isDragging.current = false;
	}, [items, selectionBox, setSelectedIds]);

	useEffect(() => {
		window.addEventListener('mouseup', handleMouseUp);
		return () => window.removeEventListener('mouseup', handleMouseUp);
	}, [handleMouseUp]);

	useEffect(() => {
		if (isDraggingItem) {
			setSelectionBox(null);
			initialPoint.current = null;
			isDragging.current = false;
		}
	}, [isDraggingItem]);

	return {
		selectionBox,
		handleMouseDown,
		handleMouseMoveSelectionBox,
		handleMouseUp,
		isDragging: isDragging.current,
	};
};
